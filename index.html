<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDP Idea Tracking System - Login</title>

    <!-- Material-UI CSS -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <link rel="stylesheet" href="https://unpkg.com/@mui/material@latest/umd/material-ui.production.min.css" />

    <!-- Google Fonts: Roboto -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Link to the external stylesheet -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root"></div>

    <!-- Material-UI JavaScript -->
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@mui/material@latest/umd/material-ui.production.min.js"></script>

    <div class="page-container">
        <div class="top-controls">
            <!-- Language Switcher -->
            <div class="language-switcher-container">
                <select id="language-switcher" class="language-switcher mui-select">
                    <option value="en">English</option>
                    <option value="es">Español</option>
                    <option value="de">Deutsch</option>
                </select>
                <i class="material-icons language-icon">language</i>
            </div>
        </div>

        <!-- Header Content -->
        <header class="page-header">
            <h1 class="main-title" data-lang-key="mainTitle">CDP Idea Tracking System</h1>
        </header>

        <main class="main-content">
            <!-- Login Form Card -->
            <div class="login-card mui-card">
                <div class="card-header">
                    <h2 class="form-title" data-lang-key="formTitle">Member Login</h2>
                    <p class="form-subtitle" data-lang-key="formSubtitle">Access your CDP dashboard</p>
                </div>

                <div class="card-content">
                    <form id="login-form">
                        <!-- Auto-filled credentials display -->
                        <div class="credentials-display">
                            <div class="credential-item">
                                <div class="credential-label">
                                    <i class="material-icons">person</i>
                                    <span data-lang-key="userIdLabel">Username</span>
                                </div>
                                <div class="credential-value">admin</div>
                            </div>

                            <div class="credential-item">
                                <div class="credential-label">
                                    <i class="material-icons">lock</i>
                                    <span data-lang-key="passwordLabel">Password</span>
                                </div>
                                <div class="credential-value">•••••</div>
                            </div>
                        </div>

                        <!-- Auto-login info -->
                        

                        <!-- Success/Error Message Box -->
                        <div id="message-box" class="message-box"></div>
                    </form>
                </div>

                <!-- Login Button -->
                <div class="card-actions">
                    <button type="submit" form="login-form" class="login-button mui-button">
                        <i class="material-icons">login</i>
                        <span data-lang-key="loginButton">Login to Dashboard</span>
                    </button>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="page-footer">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-item">
                        <i class="material-icons">support_agent</i>
                        <div class="footer-text">
                            <p class="footer-title" data-lang-key="helplineTitle">Customer Support</p>
                            <a href="mailto:<EMAIL>" class="footer-link">
                                <EMAIL>
                            </a>
                            <p class="footer-phone">1800-200-2600</p>
                        </div>
                    </div>
                </div>
                <div class="footer-section">
                    <div class="footer-item">
                        <i class="material-icons">copyright</i>
                        <div class="footer-text">
                            <p class="footer-copyright" data-lang-key="copyright">
                                Copyright © 2025 Quest Informatics Pvt Ltd. All rights reserved.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Link to the external JavaScript file -->
    <script src="script.js"></script>
</body>
</html>
