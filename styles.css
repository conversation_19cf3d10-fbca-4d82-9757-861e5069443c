/* Material-UI Reset and Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Material-UI Base Typography and Layout */
body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.5;
    letter-spacing: 0.00938em;

    /* Maintain existing brand gradient background */
    background: #41535D;
    background: linear-gradient(90deg, rgba(65, 83, 93, 1) 20%, rgba(217, 221, 223, 1) 100%);
    background-image: url('FINALISED_LOGIN_IMG_compressed_JPG.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-blend-mode: overlay;
    background-attachment: fixed;

    color: #ffffff;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;

    /* Material-UI elevation and depth */
    position: relative;
}

/* Enhanced responsive background */
@media (max-width: 1024px) {
    body {
        background-attachment: scroll;
        background-size: cover;
        background-position: center center;
    }
}

@media (max-width: 768px) {
    body {
        background-size: cover;
        background-position: center center;
    }
}

@media (max-width: 480px) {
    body {
        background-size: cover;
        background-position: center top;
    }
}

/* Material-UI Layout System */
.page-container {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    display: flex;
    flex-direction: column;

    /* Material-UI spacing system (8px base unit) */
    --mui-spacing-1: 8px;
    --mui-spacing-2: 16px;
    --mui-spacing-3: 24px;
    --mui-spacing-4: 32px;
    --mui-spacing-5: 40px;
    --mui-spacing-6: 48px;
    --mui-spacing-8: 64px;

    /* Material-UI brand colors */
    --mui-primary: #41535D;
    --mui-primary-dark: #2c3e50;
    --mui-secondary: #D9DDDF;
    --mui-surface: rgba(255, 255, 255, 0.98);
    --mui-on-surface: #41535D;
}

.main-content {
    display: flex;
    flex: 1;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 280px);
    padding: var(--mui-spacing-4) var(--mui-spacing-3);

    /* Material-UI container max-width */
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Material-UI Header Design */
.page-header {
    text-align: center;
    margin-bottom: var(--mui-spacing-4);
    padding: var(--mui-spacing-3) var(--mui-spacing-3) 0;

    /* Material-UI elevation */
    position: relative;
    z-index: 2;
}

.main-title {
    /* Material-UI Typography - h2 variant */
    font-size: 3.75rem;
    font-weight: 300;
    line-height: 1.2;
    letter-spacing: -0.00833em;
    margin: 0 0 var(--mui-spacing-2) 0;
    color: white;

    /* Enhanced text shadow for better readability */
    text-shadow:
        0 2px 4px rgba(0,0,0,0.9),
        0 4px 8px rgba(0,0,0,0.7),
        0 8px 16px rgba(0,0,0,0.5),
        0 16px 32px rgba(0,0,0,0.3);

    /* Material-UI responsive typography */
    transition: font-size 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Material-UI Card Component */
.mui-card.login-card {
    width: 100%;
    max-width: 480px;
    background-color: var(--mui-surface);
    border-radius: 16px;
    overflow: hidden;

    /* Material-UI elevation level 8 */
    box-shadow:
        0px 5px 5px -3px rgba(0,0,0,0.2),
        0px 8px 10px 1px rgba(0,0,0,0.14),
        0px 3px 14px 2px rgba(0,0,0,0.12);

    /* Enhanced backdrop blur */
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);

    /* Material-UI transitions */
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

    /* Improved spacing */
    margin: var(--mui-spacing-2);
}

.mui-card.login-card:hover {
    /* Material-UI elevation level 12 on hover */
    box-shadow:
        0px 7px 8px -4px rgba(0,0,0,0.2),
        0px 12px 17px 2px rgba(0,0,0,0.14),
        0px 5px 22px 4px rgba(0,0,0,0.12);
}

.card-header {
    background: linear-gradient(135deg, var(--mui-primary), var(--mui-primary-dark));
    color: white;
    padding: var(--mui-spacing-5) var(--mui-spacing-4);
    text-align: center;
    position: relative;
}

.form-title {
    /* Material-UI Typography - h4 variant */
    font-size: 2.125rem;
    font-weight: 400;
    line-height: 1.235;
    letter-spacing: 0.00735em;
    margin: 0 0 var(--mui-spacing-1) 0;
    color: white;
}

.form-subtitle {
    /* Material-UI Typography - body1 variant */
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    letter-spacing: 0.00938em;
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    opacity: 0.9;
}

.card-content {
    padding: var(--mui-spacing-5) var(--mui-spacing-4) var(--mui-spacing-4);
    color: var(--mui-on-surface);
}

/* Material-UI Form Components */
#login-form {
    width: 100%;
}

/* Credentials Display Section */
.credentials-display {
    margin-bottom: var(--mui-spacing-4);
}

.credential-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--mui-spacing-2) var(--mui-spacing-3);
    margin-bottom: var(--mui-spacing-2);
    background-color: rgba(65, 83, 93, 0.04);
    border-radius: 8px;
    border: 1px solid rgba(65, 83, 93, 0.12);

    /* Material-UI transitions */
    transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.credential-item:hover {
    background-color: rgba(65, 83, 93, 0.08);
}

.credential-label {
    display: flex;
    align-items: center;
    gap: var(--mui-spacing-1);

    /* Material-UI Typography - body2 variant */
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.43;
    letter-spacing: 0.01071em;
    color: var(--mui-on-surface);
}

.credential-label .material-icons {
    font-size: 20px;
    color: var(--mui-primary);
}

.credential-value {
    /* Material-UI Typography - body1 variant */
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    letter-spacing: 0.00938em;
    color: var(--mui-on-surface);
    font-family: 'Roboto Mono', monospace;
}

/* Auto-login Information */
.auto-login-info {
    display: flex;
    align-items: center;
    gap: var(--mui-spacing-1);
    padding: var(--mui-spacing-2);
    margin-bottom: var(--mui-spacing-3);
    background-color: rgba(33, 150, 243, 0.08);
    border-radius: 8px;
    border-left: 4px solid #2196F3;

    /* Material-UI Typography - caption variant */
    font-size: 0.75rem;
    font-weight: 400;
    line-height: 1.66;
    letter-spacing: 0.03333em;
    color: #1976D2;
}

.auto-login-info .material-icons {
    font-size: 16px;
    color: #2196F3;
}

/* Material-UI Button Component */
.mui-button.login-button {
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--mui-spacing-1);

    /* Material-UI Typography - button variant */
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.75;
    letter-spacing: 0.02857em;
    text-transform: uppercase;

    /* Material-UI contained button styling */
    background-color: var(--mui-primary);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    outline: none;

    /* Material-UI elevation level 2 */
    box-shadow:
        0px 3px 1px -2px rgba(0,0,0,0.2),
        0px 2px 2px 0px rgba(0,0,0,0.14),
        0px 1px 5px 0px rgba(0,0,0,0.12);

    /* Material-UI transitions */
    transition:
        background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
        box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
        border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
        color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.mui-button.login-button:hover {
    background-color: var(--mui-primary-dark);

    /* Material-UI elevation level 4 on hover */
    box-shadow:
        0px 2px 4px -1px rgba(0,0,0,0.2),
        0px 4px 5px 0px rgba(0,0,0,0.14),
        0px 1px 10px 0px rgba(0,0,0,0.12);
}

.mui-button.login-button:active {
    /* Material-UI elevation level 8 on active */
    box-shadow:
        0px 5px 5px -3px rgba(0,0,0,0.2),
        0px 8px 10px 1px rgba(0,0,0,0.14),
        0px 3px 14px 2px rgba(0,0,0,0.12);
}

.mui-button.login-button:disabled {
    background-color: rgba(0, 0, 0, 0.12);
    color: rgba(0, 0, 0, 0.26);
    box-shadow: none;
    cursor: not-allowed;
}

.login-button .material-icons {
    font-size: 20px;
}

.card-actions {
    padding: 0 var(--mui-spacing-4) var(--mui-spacing-5);
}

/* Message Box Component */
.message-box {
    display: none;
    padding: var(--mui-spacing-2);
    margin: var(--mui-spacing-3) 0 0;
    border-radius: 8px;

    /* Material-UI Typography - body2 variant */
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.43;
    letter-spacing: 0.01071em;
    text-align: center;

    /* Material-UI transitions */
    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.message-box.error {
    background-color: rgba(244, 67, 54, 0.08);
    border: 1px solid rgba(244, 67, 54, 0.2);
    color: #d32f2f;
}

.message-box.success {
    background-color: rgba(76, 175, 80, 0.08);
    border: 1px solid rgba(76, 175, 80, 0.2);
    color: #2e7d32;
}

.message-box.info {
    background-color: rgba(33, 150, 243, 0.08);
    border: 1px solid rgba(33, 150, 243, 0.2);
    color: #1976d2;
}

/* Material-UI Footer Component */
.page-footer {
    margin-top: auto;
    padding: var(--mui-spacing-4) var(--mui-spacing-3) var(--mui-spacing-3);
    width: 100%;

    /* Enhanced background with better opacity */
    background: linear-gradient(to top, rgba(65, 83, 93, 0.95), rgba(65, 83, 93, 0.4));
    border-top: 1px solid rgba(217, 221, 223, 0.15);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);

    /* Material-UI Typography - body2 variant */
    font-size: 0.875rem;
    color: white;
    text-shadow: 0 1px 3px rgba(0,0,0,0.8);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--mui-spacing-4);
    max-width: 1200px;
    margin: 0 auto;
    flex-wrap: wrap;
}

.footer-section {
    flex: 1;
    min-width: 250px;
}

.footer-item {
    display: flex;
    align-items: flex-start;
    gap: var(--mui-spacing-2);
}

.footer-item .material-icons {
    font-size: 24px;
    color: var(--mui-secondary);
    margin-top: 2px;
    flex-shrink: 0;
}

.footer-text {
    flex: 1;
}

.footer-title {
    /* Material-UI Typography - subtitle2 variant */
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.57;
    letter-spacing: 0.00714em;
    color: white;
    margin: 0 0 var(--mui-spacing-1) 0;
}

.footer-link {
    /* Material-UI Typography - body2 variant */
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.43;
    letter-spacing: 0.01071em;
    color: var(--mui-secondary);
    text-decoration: none;

    /* Material-UI transitions */
    transition: color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.footer-link:hover {
    color: white;
    text-decoration: underline;
}

.footer-phone {
    /* Material-UI Typography - body2 variant */
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.43;
    letter-spacing: 0.01071em;
    color: var(--mui-secondary);
    margin: var(--mui-spacing-1) 0 0 0;
}

.footer-copyright {
    /* Material-UI Typography - caption variant */
    font-size: 0.75rem;
    font-weight: 400;
    line-height: 1.66;
    letter-spacing: 0.03333em;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Material-UI Top Controls */
.top-controls {
    position: absolute;
    top: var(--mui-spacing-2);
    right: var(--mui-spacing-3);
    z-index: 10;
    display: flex;
    align-items: center;
    gap: var(--mui-spacing-2);
}

/* Material-UI Select Component for Language Switcher */
.language-switcher-container {
    position: relative;
    display: inline-block;
}

.mui-select.language-switcher {
    background-color: rgba(255, 255, 255, 0.98);
    color: var(--mui-on-surface);
    border: 1px solid rgba(217, 221, 223, 0.6);
    border-radius: 8px;
    padding: 12px 40px 12px 16px;

    /* Material-UI Typography - body2 variant */
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.43;
    letter-spacing: 0.01071em;
    font-family: 'Roboto', sans-serif;

    /* Material-UI elevation level 1 */
    box-shadow:
        0px 2px 1px -1px rgba(0,0,0,0.2),
        0px 1px 1px 0px rgba(0,0,0,0.14),
        0px 1px 3px 0px rgba(0,0,0,0.12);

    cursor: pointer;
    outline: none;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    min-width: 140px;

    /* Material-UI transitions */
    transition:
        border-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
        box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.mui-select.language-switcher:focus {
    border-color: var(--mui-primary);
    box-shadow:
        0px 2px 1px -1px rgba(0,0,0,0.2),
        0px 1px 1px 0px rgba(0,0,0,0.14),
        0px 1px 3px 0px rgba(0,0,0,0.12),
        0 0 0 2px rgba(65, 83, 93, 0.2);
}

.mui-select.language-switcher:hover {
    border-color: rgba(65, 83, 93, 0.8);
}

.mui-select.language-switcher option {
    color: var(--mui-on-surface);
    background-color: white;
    padding: var(--mui-spacing-1);
}

.language-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--mui-primary);
    font-size: 20px;
    pointer-events: none;
}

/* Material-UI Responsive Design System */

/* Large screens (1024px and up) */
@media (min-width: 1024px) {
    .main-content {
        padding: var(--mui-spacing-6) var(--mui-spacing-4);
    }

    .mui-card.login-card {
        max-width: 520px;
    }
}

/* Medium screens (768px to 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .main-content {
        padding: var(--mui-spacing-4) var(--mui-spacing-3);
        min-height: calc(100vh - 240px);
    }

    .page-header {
        margin-bottom: var(--mui-spacing-3);
        padding: var(--mui-spacing-2) var(--mui-spacing-3) 0;
    }

    .main-title {
        font-size: 3rem;
    }

    .mui-card.login-card {
        max-width: 100%;
        margin: var(--mui-spacing-2) auto;
    }

    .card-header {
        padding: var(--mui-spacing-4) var(--mui-spacing-3);
    }

    .card-content {
        padding: var(--mui-spacing-4) var(--mui-spacing-3) var(--mui-spacing-3);
    }

    .card-actions {
        padding: 0 var(--mui-spacing-3) var(--mui-spacing-4);
    }
}

/* Small screens (up to 767px) */
@media (max-width: 767px) {
    .main-content {
        padding: var(--mui-spacing-2) var(--mui-spacing-2);
        min-height: calc(100vh - 200px);
    }

    .page-header {
        margin-bottom: var(--mui-spacing-2);
        padding: var(--mui-spacing-2);
    }

    .main-title {
        font-size: 2.5rem;
    }

    .mui-card.login-card {
        max-width: 100%;
        margin: var(--mui-spacing-1);
        border-radius: 12px;
    }

    .card-header {
        padding: var(--mui-spacing-3) var(--mui-spacing-2);
    }

    .form-title {
        font-size: 1.75rem;
    }

    .card-content {
        padding: var(--mui-spacing-3) var(--mui-spacing-2) var(--mui-spacing-2);
    }

    .card-actions {
        padding: 0 var(--mui-spacing-2) var(--mui-spacing-3);
    }

    .top-controls {
        position: relative;
        top: auto;
        right: auto;
        justify-content: center;
        margin-bottom: var(--mui-spacing-1);
        padding: var(--mui-spacing-2);
        flex-wrap: wrap;
        gap: var(--mui-spacing-1);
    }

    .footer-content {
        flex-direction: column;
        gap: var(--mui-spacing-3);
        text-align: center;
    }

    .footer-section {
        min-width: auto;
    }

    .footer-item {
        justify-content: center;
    }

    .page-footer {
        padding: var(--mui-spacing-3) var(--mui-spacing-2) var(--mui-spacing-2);
    }
}

/* Extra small screens (up to 479px) */
@media (max-width: 479px) {
    .main-content {
        padding: var(--mui-spacing-1);
        min-height: calc(100vh - 160px);
    }

    .page-header {
        margin-bottom: var(--mui-spacing-2);
        padding: var(--mui-spacing-1);
    }

    .main-title {
        font-size: 2rem;
    }

    .mui-card.login-card {
        margin: var(--mui-spacing-1) 0;
        border-radius: 8px;
    }

    .card-header {
        padding: var(--mui-spacing-2);
    }

    .form-title {
        font-size: 1.5rem;
    }

    .form-subtitle {
        font-size: 0.875rem;
    }

    .card-content {
        padding: var(--mui-spacing-2);
    }

    .card-actions {
        padding: 0 var(--mui-spacing-2) var(--mui-spacing-2);
    }

    .top-controls {
        flex-direction: column;
        gap: var(--mui-spacing-2);
        padding: var(--mui-spacing-1);
    }

    .mui-select.language-switcher {
        min-width: 120px;
        padding: 10px 36px 10px 14px;
        font-size: 0.8125rem;
    }

    .credential-item {
        padding: var(--mui-spacing-1) var(--mui-spacing-2);
    }

    .credential-label {
        font-size: 0.8125rem;
    }

    .credential-value {
        font-size: 0.875rem;
    }
}

/* Material-UI Accessibility Enhancements */

/* High contrast mode support */
@media (prefers-contrast: high) {
    .main-title {
        text-shadow: 0 0 8px rgba(0,0,0,1);
    }

    .mui-card.login-card {
        background-color: rgba(255, 255, 255, 1) !important;
        border: 2px solid #000;
    }

    .credential-item {
        border: 2px solid var(--mui-primary);
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Material-UI Focus Management */
.mui-button.login-button:focus-visible,
.mui-select.language-switcher:focus-visible {
    outline: 2px solid var(--mui-primary);
    outline-offset: 2px;
}

/* Enhanced Loading States */
.mui-button.login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.mui-button.login-button.loading {
    position: relative;
    color: transparent;
}

.mui-button.login-button.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Backdrop blur support with fallbacks */
@supports (backdrop-filter: blur(16px)) {
    .mui-card.login-card {
        backdrop-filter: blur(16px);
        -webkit-backdrop-filter: blur(16px);
    }

    .page-footer {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
}

@supports not (backdrop-filter: blur(16px)) {
    .mui-card.login-card {
        background-color: rgba(255, 255, 255, 0.98) !important;
    }

    .page-footer {
        background: linear-gradient(to top, rgba(65, 83, 93, 0.98), rgba(65, 83, 93, 0.6));
    }
}

/* Print styles */
@media print {
    .page-footer,
    .top-controls {
        display: none;
    }

    .mui-card.login-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* Material-UI Ripple Effect Simulation */
.mui-button.login-button {
    position: relative;
    overflow: hidden;
}

.mui-button.login-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.mui-button.login-button:active::before {
    width: 300px;
    height: 300px;
}
