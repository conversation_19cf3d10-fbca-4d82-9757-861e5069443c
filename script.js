document.addEventListener('DOMContentLoaded', () => {

    // --- Enhanced Language and Translations for Material-UI Design ---
    const translations = {
        en: {
            mainTitle: "CDP Idea Tracking System",
            formTitle: "Member Login",
            formSubtitle: "Access your CDP dashboard",
            userIdLabel: "Username",
            passwordLabel: "Password",
            loginButton: "Login to Dashboard",
            helplineTitle: "Customer Support",
            copyright: "Copyright © 2025 Quest Informatics Pvt Ltd. All rights reserved.",
            autoLoginInfo: "Credentials are pre-configured for quick access",
            loginSuccess: "Login successful! Redirecting to dashboard...",
            loginError: "Authentication failed. Please try again.",
            loggingIn: "Authenticating...",
        },
        es: {
            mainTitle: "Sistema de Seguimiento de Ideas CDP",
            formTitle: "Acceso de Miembros",
            formSubtitle: "Accede a tu panel CDP",
            userIdLabel: "Nombre de Usuario",
            passwordLabel: "Contraseña",
            loginButton: "Acceder al Panel",
            helplineTitle: "Soporte al Cliente",
            copyright: "Copyright © 2025 Quest Informatics Pvt Ltd. Todos los derechos reservados.",
            autoLoginInfo: "Las credenciales están preconfiguradas para acceso rápido",
            loginSuccess: "¡Inicio de sesión exitoso! Redirigiendo al panel...",
            loginError: "Error de autenticación. Por favor, inténtelo de nuevo.",
            loggingIn: "Autenticando...",
        },
        de: {
            mainTitle: "CDP Ideen-Tracking-System",
            formTitle: "Mitglieder-Login",
            formSubtitle: "Zugang zu Ihrem CDP-Dashboard",
            userIdLabel: "Benutzername",
            passwordLabel: "Passwort",
            loginButton: "Zum Dashboard anmelden",
            helplineTitle: "Kundensupport",
            copyright: "Copyright © 2025 Quest Informatics Pvt Ltd. Alle Rechte vorbehalten.",
            autoLoginInfo: "Anmeldedaten sind für schnellen Zugang vorkonfiguriert",
            loginSuccess: "Anmeldung erfolgreich! Weiterleitung zum Dashboard...",
            loginError: "Authentifizierung fehlgeschlagen. Bitte versuchen Sie es erneut.",
            loggingIn: "Authentifizierung...",
        }
    };

    // --- Language Management ---
    const languageSwitcher = document.getElementById('language-switcher');
    let currentLang = 'en';

    function setLanguage(lang) {
        currentLang = lang;

        // Update all elements with data-lang-key attribute
        document.querySelectorAll('[data-lang-key]').forEach(elem => {
            const key = elem.getAttribute('data-lang-key');
            const translation = translations[lang][key];

            if (translation) {
                // Handle different element types
                if (elem.tagName === 'INPUT' && elem.type === 'submit') {
                    elem.value = translation;
                } else {
                    elem.textContent = translation;
                }
            }
        });

        // Store language preference
        localStorage.setItem('preferred-language', lang);

        // Update language switcher value
        if (languageSwitcher) {
            languageSwitcher.value = lang;
        }
    }

    // Initialize language from localStorage or default to English
    const savedLang = localStorage.getItem('preferred-language') || 'en';
    setLanguage(savedLang);

    // Language switcher event listener
    if (languageSwitcher) {
        languageSwitcher.addEventListener('change', (e) => {
            setLanguage(e.target.value);
        });
    }

    // --- Hardcoded Authentication System ---
    // Credentials: admin / 12345 (hardcoded for auto-login)

    function showMessage(type, message) {
        const messageBox = document.getElementById('message-box');
        if (!messageBox) return;

        messageBox.className = `message-box ${type}`;
        messageBox.textContent = message;
        messageBox.style.display = 'block';
    }

    function hideMessage() {
        const messageBox = document.getElementById('message-box');
        if (messageBox) {
            messageBox.style.display = 'none';
            messageBox.className = 'message-box';
        }
    }

    // --- Enhanced Login System ---
    function performLogin() {
        const loginButton = document.querySelector('.login-button');
        if (!loginButton) return;

        // Show loading state
        const originalContent = loginButton.innerHTML;
        loginButton.innerHTML = `<i class="material-icons">hourglass_empty</i> ${translations[currentLang].loggingIn}`;
        loginButton.disabled = true;
        loginButton.classList.add('loading');

        // Simulate authentication process
        setTimeout(() => {
            // Always succeed with hardcoded credentials
            showMessage('success', translations[currentLang].loginSuccess);

            // Simulate redirect after success
            setTimeout(() => {
                // In a real application, this would redirect to the dashboard
                console.log('Redirecting to dashboard...');

                // Reset for demo purposes
                resetLoginForm(loginButton, originalContent);
            }, 2000);
        }, 1500);
    }

    function resetLoginForm(loginButton, originalContent) {
        loginButton.innerHTML = originalContent;
        loginButton.disabled = false;
        loginButton.classList.remove('loading');
        hideMessage();
    }

    // --- Login Form Event Handling ---
    const loginForm = document.getElementById('login-form');

    if (loginForm) {
        loginForm.addEventListener('submit', function(event) {
            event.preventDefault();
            hideMessage();
            performLogin();
        });
    }

    // --- Page Initialization ---
    function initializePage() {
        // Set initial language
        setLanguage(savedLang);

        // Show auto-login info after page loads
        setTimeout(() => {
            showMessage('info', translations[currentLang].autoLoginInfo);
        }, 500);

        // Optional: Auto-login after showing info (uncomment to enable)
        // setTimeout(() => {
        //     performLogin();
        // }, 3000);
    }

    // Initialize the page
    initializePage();

    // --- Utility Functions ---

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
            // Refresh language when page becomes visible
            setLanguage(currentLang);
        }
    });

    // Handle window resize for responsive adjustments
    window.addEventListener('resize', () => {
        // Any responsive adjustments can be added here
        console.log('Window resized');
    });

});
